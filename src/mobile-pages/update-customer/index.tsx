import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Alert, Text, TouchableOpacity, View } from 'react-native';
import { Accordion, CheckBox, DropdownMobile, PrimaryButton, Switch, TextField } from '../../components/common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { useNavigation, useRoute } from '@react-navigation/native';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-message';
import { CustomerAddressInfo, SalesPersonDropdown, SelectLocation } from '../../components/mobile';
import { VerifyNumberMobile } from '../../components/modals';
import { UpdateCustomerLoader } from '../../components/loader-mobile';
import { CheckMark, CopyIcon } from '../../assets/svgs/icons';
import { customerNumberVerify, getAllCustomers, getCustomerDetails, updateCustomer } from '../../redux/apis/customer';
import { useAppSelector } from '../../redux/hooks';
import { getLanguages, getSalesPersonId, getTenantAppSetting, getTenantCountry, getUserType } from '../../redux/selectors';
import { validatePhoneNumber } from '../../utils/libphonenumber';
import { customerAddNotifications, USER_TYPES } from '../../constants';
import { checkBadRequest, isEmpty } from '../../utils/helpers';
import { colors } from '../../utils/theme';
import { setActiveCustomerGeocoder } from '../../redux/features/customer-slice';
import styles from './styles';

const UpdateCustomer = () => {
	const { t } = useTranslation();
	const navigation = useNavigation<any>();
	const { bottom } = useSafeAreaInsets();
	const dispatch = useDispatch();
	const route = useRoute<any>();
	const { id, edit } = route.params;
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const userType = useAppSelector(getUserType);
	const salesPersonId = useAppSelector(getSalesPersonId);
	const tenantCountry = useAppSelector(getTenantCountry);
	const preferredLanguages = useAppSelector(getLanguages);
	const tenantAppSetting = useAppSelector(getTenantAppSetting);
	const { verifyingOTP, updatingCustomer, loadingCustomerInfo, priceList, salesPersons, customerDetails, activeCustomer } = useAppSelector(state => state.customer);
	const { user_id } = customerDetails;
	const [showOTPModal, setShowOTPModal] = useState(false);
	const [showLocationModal, setShowLocationModal] = useState(false);
	const [mobile, setMobile] = useState({ value: '', error: false, isValid: false, verified: false });
	const [customerType, setCustomerType] = useState<any>(priceList[0]);
	const [salesPerson, setSalesPerson] = useState<any>(salesPersons[0]);
	const [preferredLanguage, setPreferredLanguage] = useState<any>(preferredLanguages[0]);
	const [shippingDetails, setShippingDetails] = useState<any>(null);
	const [validForm, setValidForm] = useState(false);
	const [details, setDetails] = useState({
		userRoleId: id,
		customerId: '',
		customerName: '',
		legalName: '',
		accountNumber: '',
		firstName: '',
		lastName: '',
		email: '',
		customerAppRequest: false,
		customerAppAccess: false
	});
	const [selection, setSelection] = useState({
		start: 0,
		end: 0
	});

	useEffect(() => {
		getCustomerData();
	}, []);

	useEffect(() => {
		if (isEmpty(details.customerName) || isEmpty(details.legalName) || isEmpty(details.firstName) || isEmpty(details.lastName)) {
			setValidForm(false);
			return;
		}

		if (mobile.isValid === false || mobile.verified === false || shippingDetails === null) {
			setValidForm(false);
			return;
		}
		setValidForm(true);
	}, [details, mobile, shippingDetails]);

	useEffect(() => {
		if (priceList.length === 0) {
			Alert.alert(t('Please Add Price List'));
		};
		if (userType !== USER_TYPES.SALES_APP && salesPersons.length === 0) {
			Alert.alert(t('please_add_sales_person'));
		};
	}, [priceList, salesPersons]);

	useEffect(() => {
		if (Object.keys(customerDetails).length === 0) {
			return;
		}
		//console.log(JSON.stringify(customerDetails));
		const { customer_id, customer_name, customer_legal_name, external_id, price_list_id, sales_person_id, preferred_language, is_verified, customer_app_access } = customerDetails;
		const { shipping_address, shipping_region_id, shipping_city_id, shipping_country_code, shipping_mobile_number, gps_coordinates } = customerDetails;
		const { customer_first_name, customer_last_name, customer_email } = customerDetails;
		setDetails({
			...details,
			customerId: customer_id,
			customerName: customer_name,
			legalName: customer_legal_name,
			accountNumber: external_id ? external_id : '',
			firstName: customer_first_name,
			lastName: customer_last_name,
			email: customer_email,
			customerAppRequest: false,
			customerAppAccess: customer_app_access
		});
		const isValid = validatePhoneNumber(user_id?.country_code, user_id?.mobile_number);
		setMobile({ ...mobile, isValid: !!isValid, verified: is_verified, value: user_id?.mobile_number?.toString() });
		setShippingDetails({
			city: shipping_city_id?.name,
			shippingCityId: shipping_city_id?._id,
			region: shipping_region_id?.name,
			shippingRegionId: shipping_region_id?._id,
			shippingAddress: shipping_address,
			latitude: gps_coordinates?.latitude,
			longitude: gps_coordinates?.longitude,
			shippingMobileNumber: shipping_mobile_number,
			shippingCountryCode: shipping_country_code
		});
		if (price_list_id) {
			const currentPriceList = priceList.find(x => x.value === price_list_id);
			if (currentPriceList) setCustomerType(currentPriceList);
		}
		if (preferred_language) {
			const currentPreferredLanguage = preferredLanguages.find(x => x.value === preferred_language);
			if (currentPreferredLanguage) setPreferredLanguage(currentPreferredLanguage);
		}
		if (sales_person_id) {
			const currentSalesPersonId = salesPersons.find(x => x.value === sales_person_id);
			if (currentSalesPersonId) setSalesPerson(currentSalesPersonId);
		}
	}, [customerDetails]);

	const getCustomerData = async () => {
		dispatch(getCustomerDetails({ userRoleId: id }));
	};

	const onToggleOtpModal = () => {
		setShowOTPModal(!showOTPModal);
	};

	const toggleLocationModal = () => {
		setShowLocationModal(!showLocationModal);
	};

	const onChangeDetails = (key: string, value: string) => {
		setDetails({ ...details, [key]: value });
	};

	const onChangeAppAccess = (isSwitch: boolean) => {
		if (isSwitch) setDetails({ ...details, customerAppAccess: !details.customerAppAccess });
		else setDetails({ ...details, customerAppRequest: !details.customerAppRequest });
	};

	const onChangeMobile = (text: string) => {
		const mobileNumber = text.replace(/[^0-9]/g, '');
		const isValid = validatePhoneNumber(user_id?.country_code, mobileNumber);
		const isVerified = mobileNumber === customerDetails.user_id?.mobile_number?.toString();
		setMobile({ ...mobile, value: mobileNumber, verified: isVerified, isValid: !!isValid });
		setSelection({ ...selection, start: mobileNumber.length, end: mobileNumber.length });
	};

	const handleSelection = () => {
		//console.log('mobile.value.length', mobile.value.length);
		setSelection({ ...selection, start: mobile.value.length, end: mobile.value.length });
	};

	const copyToClipboard = (data: string) => {
		Clipboard.setString(data);
		Toast.show({
			text1: t('copied'),
			type: 'normal',
			position: 'top'
		});
	};

	/* Send verification code to mobile number */
	const verifyMobileNumber = async () => {
		let number = mobile.value;
		const result = number.startsWith('0');
		result === true ? number = number.substring(1) : number;
		const requestBody = {
			action: 'SEND_OTP',
			mobileNumber: number,
			countryCode: user_id.country_code
		};
		const response = await dispatch(customerNumberVerify(requestBody));
		if (!response.error) {
			onToggleOtpModal();
		} else {
			Alert.alert(t(`${checkBadRequest(response.payload)}`));
		}
	};

	const onMobileVerifySuccess = () => {
		setMobile({ ...mobile, verified: true });
		onToggleOtpModal();
	};

	const onSelectLocation = (locationDetails: any) => {
		setShippingDetails(locationDetails);
		toggleLocationModal();
	};

	const fetchCustomers = (searchKey?: string) => {
		// Load customer list
		const requestBody = {
			tenantId: currentRole?.tenant_id?._id,
			salesPersonId: salesPersonId,
			customerType: 'ALL',
			type: 'ALL',
			status: 'ACTIVE',
			sortByName: true,
			searchKey: '',
			supervisorId: ''
		};
		if (searchKey) requestBody.searchKey = searchKey;
		if (userType === USER_TYPES.SUPERVISOR_APP) {
			requestBody.supervisorId = currentRole.user_id;
		}
		const response = dispatch(getAllCustomers(requestBody));
		if (response.error) {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	const onUpdateCustomer = async () => {
		const salesId = userType !== USER_TYPES.SALES_APP ? salesPerson.value : salesPersonId;
		const requestBody = {
			...details,
			...shippingDetails,
			salesPersonId: salesId,
			shippingCountryId: tenantCountry._id,
			notifications: customerAddNotifications,
			countryCode: tenantCountry.country_code,
			deviceAccess: [],
			isActive: true,
			tenantId: currentRole?.tenant_id?._id,
			mobileNumber: mobile.value,
			preferredLanguage: preferredLanguage.value,
			priceListId: customerType.value,
			isVerified: true,
			appType: userType === USER_TYPES.SALES_APP ? USER_TYPES.SALES_APP : USER_TYPES.CUSTOMER_APP
		};
		if (requestBody.email === '' || requestBody.email === undefined) delete requestBody.email;
		const validRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
		if (requestBody.email && validRegex.test(requestBody.email.toLowerCase()) === false) {
			Alert.alert(t('invalid_email'));
			return;
		}

		const response = await dispatch(updateCustomer(requestBody));
		if (!response.error) {
			if (activeCustomer && activeCustomer._id === requestBody.userRoleId) {
				dispatch(setActiveCustomerGeocoder({
					longitude: requestBody?.longitude,
					latitude: requestBody?.latitude
				}));
			}
			fetchCustomers(); // Refresh customer list again
			navigation.goBack();
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	if (loadingCustomerInfo) {
		return <UpdateCustomerLoader />;
	}

	return (
		<>
			<View style={styles.top}>
				<KeyboardAwareScrollView
					style={styles.scrollContainer}
					showsVerticalScrollIndicator={false}
					extraHeight={-64}
					keyboardShouldPersistTaps="handled"
				>
					<Accordion
						title={t('company_information')}
						accordionStyle={styles.accordionStyle}
						titleStyle={styles.accordionTitle}
					>
						<View style={styles.accordionContent}>
							<TextField
								label={t('customer_id')}
								value={details.customerId}
								style={[styles.inputField, styles.inputFieldDisabled]}
								labelContainer={styles.inputLabelContainer}
								labelStyle={[styles.inputFieldLabel, styles.inputFieldLabelDisabled]}
								editable={false}
								rightElement={<TouchableOpacity onPress={() => copyToClipboard(details.customerId)}>
									<CopyIcon />
								</TouchableOpacity>}
							/>
							<View style={styles.inputSpacingTop} />
							<TextField
								label={t('external_id')}
								value={details.accountNumber}
								style={[styles.inputField, styles.inputFieldDisabled]}
								labelContainer={styles.inputLabelContainer}
								labelStyle={[styles.inputFieldLabel, styles.inputFieldLabelDisabled]}
								editable={false}
								rightElement={<TouchableOpacity onPress={() => copyToClipboard(details.accountNumber)}>
									<CopyIcon />
								</TouchableOpacity>}
							/>
							<View style={styles.inputSpacingTop} />
							<TextField
								label={t('customer_name')}
								required
								value={details.customerName}
								style={styles.inputField}
								labelContainer={styles.inputLabelContainer}
								labelStyle={styles.inputFieldLabel}
								onChangeText={(value) => onChangeDetails('customerName', value)}
								returnKeyType="done"
							/>
							<View style={styles.inputSpacingTop} />
							<TextField
								label={t('legal_name')}
								required
								value={details.legalName}
								style={[styles.inputField, customerDetails?.is_payment_enabled && styles.inputFieldDisabled]}
								labelContainer={styles.inputLabelContainer}
								labelStyle={[styles.inputFieldLabel, customerDetails?.is_payment_enabled && styles.inputFieldLabelDisabled]}
								editable={!customerDetails?.is_payment_enabled}
								onChangeText={(value) => onChangeDetails('legalName', value)}
								returnKeyType="done"
							/>

							{userType !== USER_TYPES.SALES_APP &&
								<SalesPersonDropdown
									salesPerson={salesPerson}
									salesPersons={salesPersons}
									setSalesPerson={setSalesPerson}
								/>
							}

							<View style={styles.inputSpacingTop} />
							<DropdownMobile
								label={t('customer_type')}
								required
								value={customerType}
								options={priceList}
								onChange={setCustomerType}
								labelContainer={styles.inputLabelContainer}
							/>
						</View>
					</Accordion>
					<Accordion
						title={t('contact_person')}
						accordionStyle={styles.accordionStyle}
						titleStyle={styles.accordionTitle}
					>
						<View style={styles.accordionContent}>
							<TextField
								label={t('first_name')}
								required
								value={details.firstName}
								style={styles.inputField}
								labelContainer={styles.inputLabelContainer}
								labelStyle={styles.inputFieldLabel}
								onChangeText={(value) => onChangeDetails('firstName', value)}
								returnKeyType="done"
							/>
							<View style={styles.inputSpacingTop} />
							<TextField
								label={t('last_name')}
								required
								value={details.lastName}
								style={styles.inputField}
								labelContainer={styles.inputLabelContainer}
								labelStyle={styles.inputFieldLabel}
								onChangeText={(value) => onChangeDetails('lastName', value)}
								returnKeyType="done"
							/>
							<View style={styles.inputSpacingTop} />
							<TextField
								label={t('email')}
								value={details.email}
								style={styles.inputField}
								labelContainer={styles.inputLabelContainer}
								labelStyle={styles.inputFieldLabel}
								onChangeText={(value) => onChangeDetails('email', value)}
								returnKeyType="done"
							/>
							<View style={styles.inputSpacingTop} />
							<DropdownMobile
								label={t('preferred_language')}
								required
								value={preferredLanguage}
								options={preferredLanguages}
								onChange={setPreferredLanguage}
								labelContainer={styles.inputLabelContainer}
							/>
							<View style={styles.mobileRow}>
								<View style={styles.mobileCountryCode}>
									<Text style={styles.mobileCountryCodeTxt}>{user_id?.country_code}</Text>
								</View>
								<TextField
									inputContainerStyle={styles.mobileNumber}
									required
									autoFocus={edit ? true : false}
									label={t('mobile_number')}
									selection={selection}
									onFocus={handleSelection}
									value={mobile.value}
									placeholder="5xxx"
									style={styles.mobileNumberInput}
									labelContainer={styles.inputLabelContainer}
									labelStyle={styles.inputFieldLabel}
									keyboardType="number-pad"
									onChangeText={onChangeMobile}
									returnKeyType="done"
								/>
								{
									mobile.isValid && <View style={styles.verifyPhone}>
										<CheckMark fill={colors.secondary} />
										{
											mobile.verified && <Text style={styles.verifyPhoneTxt}>{t('verified')}</Text>
										}
									</View>
								}
							</View>
							{
								!mobile.verified && <PrimaryButton
									title={t('verify_number')}
									onPress={verifyMobileNumber}
									style={styles.verifyButton}
									titleStyle={styles.verifyButtonTxt}
									disabled={!mobile.isValid}
									loading={verifyingOTP}
								/>
							}


							{
								// Show only if mobile verified and customer app access master setting is true
								mobile.verified && (tenantAppSetting.customer_app_access ?
									<TouchableOpacity style={styles.requestAppAccess} onPress={() => onChangeAppAccess(true)}>
										<Text style={styles.requestAppAccessText}>{t('app_access')}</Text>

										<Switch
											onValueChange={() => onChangeAppAccess(true)}
											value={details.customerAppAccess}
										/>
									</TouchableOpacity>
									: (customerDetails.customer_app_access ?
										<TouchableOpacity style={styles.requestAppAccess} onPress={() => onChangeAppAccess(true)}>
											<Text style={styles.requestAppAccessText}>{t('app_access')}</Text>

											<Switch
												onValueChange={() => onChangeAppAccess(true)}
												value={details.customerAppAccess}
											/>
										</TouchableOpacity>
										:
										<TouchableOpacity disabled={customerDetails.customer_app_request} style={styles.requestAppAccess} onPress={() => onChangeAppAccess(false)}>
											<Text style={styles.requestAppAccessText}>{t('app_access')}</Text>

											{customerDetails.customer_app_request ?
												<Text style={[styles.requestAppAccessText, customerDetails.customer_app_request && { color: colors.secondary }]}>{t('requested')}</Text>
												:
												<View style={styles.flexRow}>
													<Text style={styles.requestAppAccessText}>{t('request_access') + '  '}</Text>

													<CheckBox checked={customerDetails.customer_app_request ? customerDetails.customer_app_request : details.customerAppRequest} onChange={() => onChangeAppAccess(false)} />
												</View>}
										</TouchableOpacity>))}

							<VerifyNumberMobile
								countryCode={user_id?.country_code}
								mobileNumber={mobile.value}
								isVisible={showOTPModal}
								onCancel={onToggleOtpModal}
								onVerified={onMobileVerifySuccess}
								selectedCustomerId={id}
							/>
						</View>
					</Accordion>
					<Accordion
						title={t('address')}
						accordionStyle={styles.accordionStyle}
						titleStyle={styles.accordionTitle}
					>
						<View style={[styles.accordionContent, styles.addressContent]}>
							<CustomerAddressInfo
								addressInfo={shippingDetails}
								upperContainer={true}
								onEdit={toggleLocationModal}
							/>
							<SelectLocation
								isVisible={showLocationModal}
								onClose={toggleLocationModal}
								onSelect={onSelectLocation}
								shippingNumber={shippingDetails?.shippingMobileNumber}
								initialLocation={shippingDetails ? {
									latitude: shippingDetails.latitude,
									longitude: shippingDetails.longitude,
									address: shippingDetails.shippingAddress,
									city: shippingDetails.city,
									region: shippingDetails.region
								} : null}
							/>
						</View>
					</Accordion>
				</KeyboardAwareScrollView>
			</View>
			<View style={styles.bottom}>
				<PrimaryButton
					title={t('save')}
					onPress={onUpdateCustomer}
					style={[styles.addCustomerBtn, bottom > 0 && { marginBottom: bottom }]}
					titleStyle={styles.addCustomerBtnText}
					disabled={(!validForm)}
					loading={updatingCustomer}
				/>
			</View>
		</>
	);
};
export default UpdateCustomer;