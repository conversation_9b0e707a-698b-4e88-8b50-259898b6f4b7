/* eslint-disable react-native/no-inline-styles */
import React, { memo, useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, Alert, FlatList, I18nManager, KeyboardAvoidingView, Platform, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import Modal from 'react-native-modal';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { CheckBox, PrimaryButton } from '../common';
import { VerifyNumberMobile } from '../modals';
import { ConfirmVerifyNumber } from './add-customer';
import { SelectLocation } from './select-location/SelectLocation';
import { ArrowDropDown, CloseCircle, Search, SelectedCustomer, PlusVector } from '../../assets/svgs/icons';
import { ACTIVITY_TYPE, HORIZONTAL_DIMENS, USER_TYPES, VERTICAL_DIMENS } from '../../constants';
import { setCustomerGPSCoordinates, setCustomerVerified, setSelectedCustomer } from '../../redux/features/customer-slice';
import { clearCategories } from '../../redux/features/catalog-slice';
import { customerNumberVerify, getAllCustomers, getCustomerAllowValidation } from '../../redux/apis/customer';
import { useAppSelector } from '../../redux/hooks';
import { getBranchId, getSalesPersonId, getUserType } from '../../redux/selectors';
import { colors, fonts } from '../../utils/theme';
import { getCategory } from '../../redux/apis/product';
import { clearCartList } from '../../redux/features/cart-slice';
import { checkBadRequest } from '../../utils/helpers';
import Distance from './Distance';
import useSelectCustomer from '../../hooks/useSelectCustomer';
import { setSelectedCustomerForVisit } from '../../redux/features/tracking-slice';
import { trimText } from '../../utils/textTrim';
import { CustomerType } from '../../types';
import AlphabeticalSectionList from './AlphabeticalList';
import useStatusBarHeight from '../../hooks/useStatusbarHeight';

const SelectCustomer = memo(() => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const navigation = useNavigation<NativeStackNavigationProp<any>>();
	const userType = useAppSelector(getUserType);
	const branchId = useAppSelector(getBranchId);
	const salesPersonId = useAppSelector(getSalesPersonId);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const activeCustomer = useAppSelector(state => state.customer.activeCustomer);
	const masterSettings = useAppSelector(state => state.setting.masterSettings);
	const activeShift = useAppSelector(state => state.tracking.activeShift);
	const activeActivity = useAppSelector(state => state.tracking.activeActivity);
	const startShiftAutoLocation = useAppSelector(state => state.tracking.startShiftAutoLocation);
	const selectCustomerForVisit = useAppSelector(state => state.tracking.selectCustomerForVisit);
	const { customers, loading } = useAppSelector(state => state.customer);
	const [showVerifyConfirm, setShowVerifyConfirm] = useState(false);
	const [verifyLoader, setVerifyloader] = useState(false);
	const [searchString, setSearchString] = useState('');
	const [searchResults, setSearchResults] = useState<any[]>([]);
	const [showSearchResults, setShowSearchResults] = useState(false);
	const [showOTPModal, setShowOTPModal] = useState(false);
	const [verifyCustomer, setVerifyCustomer] = useState<any>(null);
	const [selectedCustomerId, setSelectedCustomerId] = useState<string>('');
	const [checkingAdd, setCheckingAdd] = useState(false);
	const [showLocationModal, setShowLocationModal] = useState(false);
	const userCurrentLocation = useAppSelector(state => state.tracking.currentLocation);
	const { showCustomerSelectModal, toggleCustomerSelectModal } = useSelectCustomer();

	useEffect(() => {
		setShowSearchResults(false); // Hide search results on close
		setSearchString('');
		fetchCustomers();
	}, [showCustomerSelectModal]);
	useFocusEffect(
		React.useCallback(() => {
			// Clear the search text whenever the screen gains focus
			setShowSearchResults(false); // Hide search results on close
			setSearchString('');
			fetchCustomers();
		}, [])
	);
	useEffect(() => {
		if (activeCustomer !== null) {
			setSelectedCustomerId(activeCustomer._id); // Make checkbox selected if customer is selected already before
		};
	}, [activeCustomer, showCustomerSelectModal]);

	const fetchCustomers = (searchKey?: string) => {
		// Load customer list
		const requestBody = {
			tenantId: currentRole?.tenant_id?._id,
			salesPersonId: salesPersonId,
			supervisorId: '',
			customerType: 'ALL',
			type: 'ALL',
			status: 'ACTIVE',
			sortByName: true,
			searchKey: ''
		};
		if (searchKey) requestBody.searchKey = searchKey;
		if (userType === USER_TYPES.SUPERVISOR_APP) {
			requestBody.supervisorId = currentRole.user_id;
		}
		dispatch(getAllCustomers(requestBody));
	};

	const toggleSelectCustomer = () => {
		toggleCustomerSelectModal(); // Hide show select customer modal
		if (showCustomerSelectModal === true) {
			setSearchString(''); // Clear search input on close
			setShowSearchResults(false); // Hide search results on close
			setSearchResults([]); // Clear search results on close
		}
	};

	const toggleLocationModal = () => {
		setShowLocationModal(!showLocationModal);
	};

	const goToAddCustomer = async () => {
		setCheckingAdd(true);
		const requestParams = {
			tenantId: currentRole?.tenant_id?._id,
			type: 'CUSTOMERS'
		};
		const response = await dispatch(getCustomerAllowValidation(requestParams));
		setCheckingAdd(false);
		if (response.error) {
			Alert.alert(t(checkBadRequest(response.payload)));
		} else {
			const data = response.payload.data;
			// Check if customer limit is exceed or not
			if (data?.activeCustomers < data?.allowCustomers) {
				toggleCustomerSelectModal();
				navigation.navigate('AddCustomer'); // Navigate to add customer
			} else {
				Alert.alert(t('customer_limit_exceed'));
			}
		}
	};

	const onSearch = (value: string) => {
		const showSearchResultsFlag = value.trim().length > 0;
		let searchText = trimText(value);
		setSearchString(searchText);
		setShowSearchResults(showSearchResultsFlag);
		const searchedValue = searchText.toLowerCase();
		// console.log('searchedValue', searchedValue);
		const filteredCustomers = customers.filter((x) => {
			// console.log('match', x.value.toLowerCase(), x.value.toLowerCase().includes(searchedValue));
			return x.value.toLowerCase().includes(searchedValue) || x.external_id?.toLowerCase()?.includes(searchedValue);
		});
		// console.log('filteredCustomers', filteredCustomers.length);
		setSearchResults(filteredCustomers);
	};

	const handleCancel = () => {
		setShowVerifyConfirm(!showVerifyConfirm);
	};

	const handleVerify = () => {
		// setShowVerifyConfirm(!showVerifyConfirm);
		setVerifyloader(true);
		verifyMobileNumber(verifyCustomer); // Send verification otp
	};

	const handleEdit = () => {
		handleCancel();
		toggleCustomerSelectModal();
		navigation.navigate('UpdateCustomer', { id: selectedCustomerId, edit: true });
	};

	const verifyMobileNumber = async (selectedCustomer: any) => {
		let number = selectedCustomer?.customer_mobile_number;
		const result = number?.toString().startsWith('0');
		result === true ? number = number.substring(1) : number;
		//console.log('selectedCustomer in verify mobile number', selectedCustomer);
		const requestBody = {
			action: 'SEND_OTP',
			mobileNumber: number,
			countryCode: selectedCustomer?.customer_country_code
		};
		const response = await dispatch(customerNumberVerify(requestBody));
		setVerifyloader(false);
		if (!response.error) {
			setShowVerifyConfirm(false); // Close confirmation modal
			setTimeout(() => {
				setShowOTPModal(true); // Show enter otp modal
			}, 400);
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	const onToggleOtpModal = () => {
		setShowOTPModal(!showOTPModal);
	};

	const onMobileVerifySuccess = () => {
		setShowOTPModal(false); // Close enter otp modal
		dispatch(setCustomerVerified(selectedCustomerId)); // Set is_verified true manually instead of API call
	};

	const onSelectLocation = (locationDetails: any) => {
		toggleLocationModal();
		dispatch(setCustomerGPSCoordinates({
			customerRoleId: selectedCustomerId,
			latitude: locationDetails?.latitude,
			longitude: locationDetails?.longitude
		}));
	};

	const onSelectCustomer = () => {
		if (selectedCustomerId) {
			const customer = customers.find((x) => x._id === selectedCustomerId);

			// If customer is not verified then verify first
			if (!customer?.is_verified) {
				setVerifyCustomer(customer); // Set customer in state to use country code and mobile number in verify otp modal
				setShowVerifyConfirm(!showVerifyConfirm); // Display confirmation popup for verify number
				return;
			}

			// If gps location is not set then set first
			if (customer.gps_coordinates?.latitude === 0 || customer.gps_coordinates === undefined || customer.gps_coordinates.longitude === 0) {
				toggleLocationModal();
				return;
			}

			if (activeActivity && activeActivity.activity_type === ACTIVITY_TYPE.VISIT) {
				Alert.alert(t('visit_change_customer_error'));
			} else if (selectCustomerForVisit) {
				dispatch(setSelectedCustomerForVisit(customer));
				toggleCustomerSelectModal();
			} else {
				if (customer?.is_verified) {
					dispatch(setSelectedCustomer(customer)); // Set selected customer in redux state to persist
					dispatch(setSelectedCustomerForVisit(customer));

					toggleCustomerSelectModal();
					setSearchString('');
					setShowSearchResults(false);
					setSearchResults([]);
					dispatch(clearCartList()); // Clear cart when customer changed

					/* Load fresh products again with below code when change customer */
					dispatch(clearCategories());
					setTimeout(() => {
						const requestBody: any = {
							tenantId: currentRole?.tenant_id?._id,
							priceListId: customer.price_list_id
						};
						if (masterSettings && masterSettings?.hide_out_of_stock_product) {
							requestBody.hideOutOfStock = true;
							requestBody.branchId = branchId;
						}
						dispatch(getCategory(requestBody));
					}, 500);
				}
			}
		} else {
			Alert.alert(t('please_select_customer'));
		}
	};

	const renderItem = useCallback((item: CustomerType) => {
		return (
			<TouchableOpacity style={styles.listItemContainer} onPress={() => setSelectedCustomerId(item.key)}>
				<View style={styles.nameContainer}>
					<Text style={styles.listItemLabel} numberOfLines={2} ellipsizeMode='tail'>{item.value}</Text>
					{
						item?.external_id && <Text style={styles.externalId}>{item?.external_id}</Text>
					}
				</View>
				<View style={styles.distanceContainer}>{userCurrentLocation && <Distance item={item} />}
					<CheckBox
						checked={selectedCustomerId === item.key}
						onChange={() => setSelectedCustomerId(item.key)}
					/>
				</View>
			</TouchableOpacity>
		);
	}, [customers, selectedCustomerId, userCurrentLocation]);

	const renderHeader = useCallback(
		(title: string) => (
			<View style={styles.sectionHeaderContainer}>
				<Text style={styles.sectionHeaderLabel}>{title}</Text>
			</View>
		),
		[customers]
	);

	const keyExtractor = useCallback(
		(item: CustomerType) => item._id.toString(),
		[customers]
	);

	const getItemTitle = useCallback(
		(item: CustomerType) => item.customer_name || item.customer_legal_name,
		[customers]
	);
	const statusBarHeight = useStatusBarHeight();
	return (
		<>
			{activeShift || activeActivity || startShiftAutoLocation ?
				<View style={styles.container}>
					<SelectedCustomer fill={colors.navbarContent} />
					<Text style={styles.dropdownButtonText} numberOfLines={1}>{activeCustomer?.customer_name}</Text>
					<TouchableOpacity style={styles.plusBtn} onPress={() => navigation.navigate('AddCustomer')}>
						<PlusVector height={12} width={12} fill={colors.navbarContent} />
					</TouchableOpacity>
				</View>
				: activeCustomer ?
					<TouchableOpacity style={[styles.container, styles.containerBackground]} onPress={toggleSelectCustomer}>
						{/* <User fill={colors.black} /> */}
						<Text style={styles.dropdownButtonText} numberOfLines={2} ellipsizeMode='tail'>{activeCustomer.customer_name}</Text>
						<ArrowDropDown width={32} height={32} fill={colors.navbarContent} />
					</TouchableOpacity>
					: <TouchableOpacity style={[styles.container, styles.containerBackground]} onPress={toggleSelectCustomer}>
						{/* <User fill={colors.black} /> */}
						<Text style={styles.dropdownButtonText}>{t('select_customer')}</Text>
						<ArrowDropDown width={32} height={32} fill={colors.navbarContent} />
					</TouchableOpacity>}
			<Modal
				isVisible={showCustomerSelectModal}
				style={[styles.modalContainer, { paddingTop: Platform.OS === 'ios' ? statusBarHeight : 0 }]}
			>
				<KeyboardAvoidingView
					behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
					style={{ flex: 1 }}
				>
					<View style={styles.modalContent}>
						<View style={styles.modalHeader}>
							<TouchableOpacity
								onPress={goToAddCustomer}
								style={styles.addBtn}
								disabled={checkingAdd}
							>
								{
									checkingAdd ? (
										<ActivityIndicator />
									) : (
										<Text style={styles.addText}>{t('add')}</Text>
									)
								}
							</TouchableOpacity>
							<Text style={styles.selectCustomerText}>{t('select_customer')}</Text>
							<TouchableOpacity onPress={toggleSelectCustomer} style={styles.closeBtn}>
								<CloseCircle />
							</TouchableOpacity>
						</View>
						<View style={styles.searchInputContainer}>
							<TextInput
								placeholder={t('search')}
								style={styles.searchInput}
								value={searchString}
								placeholderTextColor={colors.grey400}
								onChangeText={onSearch}
							/>
							<View style={styles.searchIconContainer}>
								<Search height={20} width={20} fill={colors.grey600} />
							</View>
						</View>
						<View style={{ flex: 1 }}>
							{
								loading && <View style={styles.loadingContainer}>
									<ActivityIndicator color={colors.primary} />
								</View>
							}
							{
								!loading && !showSearchResults &&
								<AlphabeticalSectionList
									{...{
										data: customers,
										renderItem,
										renderHeader,
										keyExtractor,
										getItemTitle
									}} />
							}
							{
								showSearchResults && <FlatList
									data={searchResults}
									style={styles.listContainer}
									renderItem={({ item }: any) => (
										<TouchableOpacity style={styles.listItemContainer} onPress={() => setSelectedCustomerId(item.key)}>
											<View style={styles.nameContainer}>
												<Text style={styles.listItemLabel} numberOfLines={2} // Increased to 2 lines
													ellipsizeMode="tail">{item.value}</Text>
												{
													item?.external_id && <Text style={styles.externalId}>{item?.external_id}</Text>
												}
											</View>
											<CheckBox
												checked={selectedCustomerId === item.key}
												onChange={() => setSelectedCustomerId(item.key)}
											/>
										</TouchableOpacity>
									)}
									ListEmptyComponent={() => <View style={styles.emptyContainer}>
										<Text style={styles.noRecordsText}>{t('no_result_found')}</Text>
									</View>}
								/>
							}
							<View style={[styles.footerContainer, { paddingBottom: Platform.OS === 'ios' ? VERTICAL_DIMENS._24 : VERTICAL_DIMENS._12 }]}>
								<PrimaryButton
									title={t('select')}
									onPress={onSelectCustomer}
									titleStyle={styles.selectCustomerBtnText}
								/>
							</View>
						</View>
						<ConfirmVerifyNumber
							heading={t('verify_mobile')}
							title={t('verify_number_confirm')}
							confirmButtonTitle={t('send_otp')}
							countryCode={verifyCustomer?.customer_country_code}
							mobileNumber={verifyCustomer?.customer_mobile_number}
							isVisible={showVerifyConfirm}
							onCancel={handleCancel}
							onConfirm={handleVerify}
							onEdit={handleEdit}
							loader={verifyLoader}
						/>
						<VerifyNumberMobile
							countryCode={verifyCustomer?.customer_country_code}
							mobileNumber={verifyCustomer?.customer_mobile_number}
							isVisible={showOTPModal}
							selectedCustomerId={selectedCustomerId}
							onCancel={onToggleOtpModal}
							onVerified={onMobileVerifySuccess}
						/>
						<SelectLocation
							isVisible={showLocationModal}
							onClose={toggleLocationModal}
							onSelect={onSelectLocation}
							customerRoleId={selectedCustomerId}
							initialLocation={userCurrentLocation ? {
								latitude: userCurrentLocation.latitude,
								longitude: userCurrentLocation.longitude
							} : null}
						/>
					</View>
				</KeyboardAvoidingView>
			</Modal>
		</>
	);
});

const styles = StyleSheet.create({
	container: {
		borderRadius: 30,
		paddingHorizontal: HORIZONTAL_DIMENS._13,
		alignItems: 'center',
		flexDirection: 'row',
		justifyContent: 'center',
		width: '100%',
		height: '100%'
		// flex: 1,

	},
	containerBackground: {
		backgroundColor: colors.grey100,
		height: VERTICAL_DIMENS._48
	},
	dropdownButtonText: {
		marginHorizontal: HORIZONTAL_DIMENS._12,
		color: colors.navbarContent,
		textAlign: 'left',
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._15,
		flex: 1,
		flexShrink: 1
	},
	plusBtn: {
		// backgroundColor: 'red',
		padding: 4,
		borderColor: colors.white,
		borderWidth: 2,
		borderRadius: 20
	},
	modalContainer: {

		margin: 0,
		flex: 1
	},
	modalContent: {
		backgroundColor: colors.white,
		flex: 1
		// borderTopLeftRadius: 12,
		// borderTopRightRadius: 12,

	},
	modalHeader: {
		alignItems: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		paddingVertical: VERTICAL_DIMENS._16,
		flexDirection: 'row',
		width: '100%'
	},
	addBtn: {
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		paddingVertical: VERTICAL_DIMENS._10,
		position: 'absolute',
		top: VERTICAL_DIMENS._6,
		zIndex: 10
	},
	addText: {
		color: colors.secondary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._16
	},
	selectCustomerText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._18,
		flex: 1,
		textAlign: 'center'
	},
	closeBtn: {
		paddingTop: VERTICAL_DIMENS._14,
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		position: 'absolute',
		top: 0,
		right: 0,
		zIndex: 10
	},
	searchInputContainer: {
		marginTop: VERTICAL_DIMENS._2,
		marginHorizontal: HORIZONTAL_DIMENS._16,
		marginBottom: VERTICAL_DIMENS._11
	},
	searchInput: {
		color: colors.primary,
		backgroundColor: colors.grey100,
		borderRadius: 20,
		paddingRight: HORIZONTAL_DIMENS._12,
		paddingLeft: HORIZONTAL_DIMENS._44,
		height: VERTICAL_DIMENS._40,
		textAlign: I18nManager.isRTL ? 'right' : 'left',
		fontSize: HORIZONTAL_DIMENS._15
	},
	searchIconContainer: {
		position: 'absolute',
		left: 14,
		justifyContent: 'center',
		height: VERTICAL_DIMENS._40
	},
	loadingContainer: {
		flex: 1
	},
	listContainer: {
		flex: 1
	},
	hideList: {
		display: 'none'
	},
	listItemContainer: {
		flex: 1,
		minHeight: VERTICAL_DIMENS._56,
		marginHorizontal: HORIZONTAL_DIMENS._16,
		// marginRight: HORIZONTAL_DIMENS._20,
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		borderTopColor: colors.grey200,
		borderTopWidth: 1,
		top: -1
	},
	noTopBorder: {
		borderTopWidth: 0
	},
	nameContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'flex-start'

	},
	listItemLabel: {

		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '400',
		flexShrink: 1,
		overflow: 'hidden'
	},
	externalId: {
		color: colors.grey500,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		fontWeight: '400'
	},
	sectionHeaderContainer: {
		height: VERTICAL_DIMENS._30,
		backgroundColor: colors.grey200,
		justifyContent: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._16
	},
	sectionHeaderLabel: {
		alignSelf: 'flex-start',
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._12,
		fontWeight: '500',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	indexLetterStyle: {
		color: colors.secondary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._10,
		fontWeight: '600'
	},
	footerContainer: {
		padding: VERTICAL_DIMENS._12
	},
	selectCustomerBtnText: {
		textTransform: 'uppercase'
	},
	emptyContainer: {
		alignItems: 'center',
		flex: 1,
		justifyContent: 'center'
	},
	noRecordsText: {
		color: colors.grey400,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._12,
		fontWeight: '500'
	},
	distanceContainer: {
		alignItems: 'center',
		flexDirection: 'row'
	}
});

export { SelectCustomer };
