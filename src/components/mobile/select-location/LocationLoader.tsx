import React from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { LocationGPS } from '../../../assets/svgs/icons';
import { colors, fonts } from '../../../utils/theme';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../constants';

const LocationLoader = () => {
	const { t } = useTranslation();

	return (
		<View style={styles.container}>
			<View style={styles.content}>
				<View style={styles.iconContainer}>
					<LocationGPS fill={colors.primary} width={48} height={48} />
					<ActivityIndicator
						size="large"
						color={colors.primary}
						style={styles.spinner}
					/>
				</View>
				<Text style={styles.title}>Finding Location</Text>
				<Text style={styles.subtitle}>Please wait while we determine your location</Text>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		...StyleSheet.absoluteFillObject,
		backgroundColor: colors.white,
		justifyContent: 'center',
		alignItems: 'center',
		zIndex: 1000
	},
	content: {
		alignItems: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._32
	},
	iconContainer: {
		position: 'relative',
		marginBottom: VERTICAL_DIMENS._24
	},
	spinner: {
		position: 'absolute',
		top: -8,
		left: -8,
		right: -8,
		bottom: -8
	},
	title: {
		fontSize: HORIZONTAL_DIMENS._18,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		color: colors.primary,
		textAlign: 'center',
		marginBottom: VERTICAL_DIMENS._8
	},
	subtitle: {
		fontSize: HORIZONTAL_DIMENS._14,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		color: colors.grey600,
		textAlign: 'center',
		lineHeight: 20
	}
});

export default LocationLoader;
